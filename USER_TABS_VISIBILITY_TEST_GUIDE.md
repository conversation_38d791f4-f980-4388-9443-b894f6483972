# WorkSpace Pro 用户标签页隐藏/显示功能测试指南

## 🎯 功能概述

新增的"隐藏/显示用户标签页"功能允许用户：
- 区分工作区配置的标签页和用户自行打开的标签页
- 一键隐藏用户自行打开的标签页到专用窗口
- 一键恢复隐藏的用户标签页到主窗口
- 在工作区切换时保持隐藏状态
- 完全保持标签页状态（表单数据、滚动位置、JavaScript状态等）

## 🚀 准备工作

### 1. 重新加载扩展
1. 打开 `chrome://extensions/`
2. 找到 WorkSpace Pro 扩展
3. 点击刷新按钮 🔄
4. 确认扩展重新加载成功

### 2. 创建测试工作区
创建至少两个工作区用于测试：
- **工作区A**：配置 `https://www.google.com` 和 `https://github.com`
- **工作区B**：配置 `https://www.baidu.com` 和 `https://stackoverflow.com`

## 📋 核心功能测试

### 测试1：基础隐藏/显示功能
**目标**：验证基本的隐藏和显示用户标签页功能

**步骤**：
1. 切换到工作区A
2. 等待自动打开配置的网站（google.com, github.com）
3. 手动打开几个额外的网站（如 youtube.com, twitter.com）
4. 在WorkSpace Pro侧边栏中，找到工作区A旁边的眼睛图标按钮
5. 点击眼睛图标（应该显示为 👁️）
6. **验证**：
   - 用户自行打开的标签页（youtube.com, twitter.com）应该消失
   - 工作区配置的标签页（google.com, github.com）应该保留
   - 按钮图标变为 🚫👁️（EyeOff）
   - 按钮上显示隐藏的标签页数量

**预期结果**：✅ 用户标签页成功隐藏，工作区配置标签页保留

### 测试2：恢复隐藏的标签页
**目标**：验证恢复隐藏标签页的功能

**步骤**：
1. 在测试1的基础上，点击 🚫👁️ 按钮
2. **验证**：
   - 之前隐藏的标签页（youtube.com, twitter.com）重新出现
   - 标签页状态完全保持（如果之前有输入内容、滚动位置等）
   - 按钮图标变回 👁️
   - 按钮上显示可隐藏的用户标签页数量

**预期结果**：✅ 隐藏的标签页成功恢复，状态完全保持

### 测试3：标签页状态保持测试
**目标**：验证隐藏/显示过程中标签页状态的完整保持

**步骤**：
1. 在用户自行打开的标签页中进行以下操作：
   - 在YouTube中播放视频到中间位置并暂停
   - 在Twitter中滚动到页面中间
   - 在任何有表单的网站中输入一些文字（不提交）
2. 隐藏用户标签页
3. 等待几分钟
4. 恢复用户标签页
5. **验证**：
   - YouTube视频仍在暂停状态，播放位置保持
   - Twitter页面滚动位置保持
   - 表单中的文字仍然存在

**预期结果**：✅ 所有标签页状态完全保持，无任何重新加载

### 测试4：工作区切换时的状态保持
**目标**：验证工作区切换时隐藏状态的保持

**步骤**：
1. 在工作区A中隐藏用户标签页
2. 切换到工作区B
3. 在工作区B中打开一些用户标签页
4. 切换回工作区A
5. **验证**：
   - 工作区A的用户标签页仍然保持隐藏状态
   - 按钮显示为 🚫👁️ 状态
   - 工作区A配置的标签页正常显示

**预期结果**：✅ 工作区切换时隐藏状态正确保持

### 测试5：多工作区独立状态管理
**目标**：验证不同工作区的隐藏状态独立管理

**步骤**：
1. 工作区A：隐藏用户标签页
2. 切换到工作区B：不隐藏用户标签页
3. 在工作区B中打开一些用户标签页，保持显示状态
4. 切换回工作区A，验证仍然是隐藏状态
5. 切换回工作区B，验证仍然是显示状态

**预期结果**：✅ 每个工作区的隐藏状态独立管理，互不影响

## 🔍 高级测试场景

### 测试6：按钮状态和计数器测试
**目标**：验证UI按钮的状态显示和计数器准确性

**步骤**：
1. 观察按钮在不同状态下的显示：
   - 无用户标签页时：按钮应该显示但无计数器
   - 有用户标签页时：显示 👁️ 和用户标签页数量
   - 隐藏后：显示 🚫👁️ 和隐藏标签页数量
2. 验证计数器数字的准确性
3. 验证鼠标悬停时的提示信息

**预期结果**：✅ 按钮状态和计数器准确反映当前状态

### 测试7：边界情况测试
**目标**：测试各种边界情况

**步骤**：
1. **空工作区测试**：在没有配置网站的工作区中测试
2. **只有配置网站测试**：只打开工作区配置的网站，无用户标签页
3. **大量标签页测试**：打开20+个用户标签页进行隐藏/显示
4. **快速切换测试**：快速多次点击隐藏/显示按钮

**预期结果**：✅ 所有边界情况都能正确处理

### 测试8：性能和稳定性测试
**目标**：验证功能的性能和稳定性

**步骤**：
1. 长时间使用隐藏/显示功能
2. 在隐藏状态下长时间工作
3. 频繁切换工作区
4. 观察内存使用情况和响应速度

**预期结果**：✅ 功能稳定，性能良好

## 🎨 UI/UX 验证

### 视觉验证清单
- [ ] 按钮图标清晰易懂（👁️ 表示显示，🚫👁️ 表示隐藏）
- [ ] 计数器数字清晰可见
- [ ] 按钮位置合理，不影响其他功能
- [ ] 鼠标悬停提示信息准确
- [ ] 加载状态有适当的视觉反馈
- [ ] 按钮在不同工作区状态下正确显示/隐藏

### 交互验证清单
- [ ] 按钮点击响应迅速
- [ ] 加载过程中按钮正确禁用
- [ ] 操作完成后状态立即更新
- [ ] 错误情况有适当的处理

## ❌ 问题排查

### 如果功能不工作，检查以下项目：

#### 1. 扩展加载问题
- 确认扩展已正确重新加载
- 检查扩展是否有错误状态

#### 2. 按钮不显示
- 确认当前工作区是活跃状态
- 检查是否有用户自行打开的标签页

#### 3. 隐藏/显示不生效
- 检查浏览器控制台是否有错误信息
- 确认标签页没有被浏览器自动管理

#### 4. 状态不保持
- 检查是否有其他扩展干扰
- 确认网站本身没有自动刷新机制

## 📊 测试结果记录

请记录每个测试的结果：

- [ ] 测试1：基础隐藏/显示功能 - ✅通过 / ❌失败
- [ ] 测试2：恢复隐藏的标签页 - ✅通过 / ❌失败
- [ ] 测试3：标签页状态保持 - ✅通过 / ❌失败
- [ ] 测试4：工作区切换状态保持 - ✅通过 / ❌失败
- [ ] 测试5：多工作区独立管理 - ✅通过 / ❌失败
- [ ] 测试6：按钮状态和计数器 - ✅通过 / ❌失败
- [ ] 测试7：边界情况测试 - ✅通过 / ❌失败
- [ ] 测试8：性能和稳定性 - ✅通过 / ❌失败

## 🎉 测试完成

如果所有测试都通过，说明"隐藏/显示用户标签页"功能已成功实现！

用户现在可以享受更加整洁和专注的工作区体验，通过一键隐藏临时标签页来保持工作区的核心内容突出显示。
