import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { copyFileSync } from 'fs'

// 自定义插件：复制workspace-placeholder文件
const copyWorkspacePlaceholderPlugin = () => {
  return {
    name: 'copy-workspace-placeholder',
    closeBundle() {
      // 在构建完成后复制workspace-placeholder文件到dist目录
      try {
        copyFileSync('public/workspace-placeholder.html', 'dist/workspace-placeholder.html')
        copyFileSync('public/workspace-placeholder.js', 'dist/workspace-placeholder.js')
        console.log('✅ Copied workspace-placeholder files to dist/')
      } catch (error) {
        console.error('❌ Failed to copy workspace-placeholder files:', error)
      }
    }
  }
}

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), copyWorkspacePlaceholderPlugin()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    outDir: 'dist',
    rollupOptions: {
      input: {
        sidepanel: resolve(__dirname, 'sidepanel.html'),
        background: resolve(__dirname, 'src/background/background.ts'),
      },
      output: {
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name === 'background') {
            return 'background.js'
          }
          return 'assets/[name]-[hash].js'
        },
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    target: 'esnext',
    minify: false, // 便于调试，生产环境可以开启
    copyPublicDir: true, // 确保public目录被复制
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  }
})
