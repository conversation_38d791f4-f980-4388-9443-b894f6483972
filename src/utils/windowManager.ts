import {
  OperationResult,
  TabInfo
} from '@/types/workspace';
import { ERROR_CODES } from './constants';

/**
 * 窗口信息接口
 */
export interface WindowInfo {
  id: number;
  workspaceId: string;
  workspaceName: string;
  tabCount: number;
  isVisible: boolean;
}

/**
 * 专用窗口管理类
 * 实现全局单例专用窗口架构 - 所有工作区共享一个专用窗口
 */
export class WindowManager {
  private static globalWorkspaceWindowId: number | null = null; // 全局专用窗口ID
  private static readonly GLOBAL_WORKSPACE_WINDOW_KEY = 'global_workspace_window';

  /**
   * 获取或创建全局专用窗口
   */
  static async getOrCreateGlobalWorkspaceWindow(): Promise<OperationResult<WindowInfo>> {
    try {
      console.log('获取或创建全局工作区专用窗口');

      // 检查是否已存在全局专用窗口
      if (this.globalWorkspaceWindowId) {
        try {
          // 验证窗口是否仍然存在
          const window = await chrome.windows.get(this.globalWorkspaceWindowId);
          if (window) {
            console.log(`全局工作区专用窗口已存在: ${this.globalWorkspaceWindowId}`);
            return {
              success: true,
              data: {
                id: this.globalWorkspaceWindowId,
                workspaceId: 'global',
                workspaceName: '全局工作区专用窗口',
                tabCount: window.tabs?.length || 0,
                isVisible: window.state !== 'minimized'
              }
            };
          }
        } catch {
          // 窗口不存在，清理引用
          console.log('全局专用窗口已不存在，需要重新创建');
          this.globalWorkspaceWindowId = null;
        }
      }

      // 创建全局专用窗口
      console.log('创建新的全局工作区专用窗口');
      const window = await chrome.windows.create({
        type: 'normal',
        state: 'normal',
        focused: false, // 不获取焦点
        width: 1200,
        height: 800,
        left: 100,
        top: 100,
        url: chrome.runtime.getURL('workspace-placeholder.html') + '?workspaceId=global&workspaceName=' + encodeURIComponent('全局工作区专用窗口')
      });

      // 创建后立即最小化窗口
      if (window.id) {
        try {
          await chrome.windows.update(window.id, { state: 'minimized' });
          console.log(`成功最小化全局工作区专用窗口 -> 窗口ID ${window.id}`);
        } catch (error) {
          console.warn(`最小化窗口失败，但窗口创建成功`, error);
        }
      }

      if (!window.id) {
        throw new Error('Failed to create global workspace window');
      }

      // 保存全局窗口ID
      this.globalWorkspaceWindowId = window.id;

      console.log(`成功创建全局工作区专用窗口 -> 窗口ID ${window.id}`);

      return {
        success: true,
        data: {
          id: window.id,
          workspaceId: 'global',
          workspaceName: '全局工作区专用窗口',
          tabCount: window.tabs?.length || 1,
          isVisible: false // 窗口默认最小化，所以不可见
        }
      };
    } catch (error) {
      console.error(`创建全局工作区专用窗口失败`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to create global workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 为工作区创建专用窗口（保持向后兼容）
   * 现在所有工作区都使用同一个全局专用窗口
   */
  static async createWorkspaceWindow(
    workspaceId: string,
    workspaceName: string
  ): Promise<OperationResult<WindowInfo>> {
    console.log(`为工作区 ${workspaceName} (${workspaceId}) 获取全局专用窗口`);
    return await this.getOrCreateGlobalWorkspaceWindow();
  }

  /**
   * 获取工作区的专用窗口ID（现在所有工作区共享同一个窗口）
   */
  static getWorkspaceWindowId(workspaceId: string): number | undefined {
    return this.globalWorkspaceWindowId || undefined;
  }

  /**
   * 获取窗口对应的工作区ID（现在返回全局标识）
   */
  static getWindowWorkspaceId(windowId: number): string | undefined {
    return windowId === this.globalWorkspaceWindowId ? 'global' : undefined;
  }

  /**
   * 获取全局专用窗口ID
   */
  static getGlobalWorkspaceWindowId(): number | undefined {
    return this.globalWorkspaceWindowId || undefined;
  }

  /**
   * 将标签页移动到全局专用窗口
   */
  static async moveTabsToWorkspaceWindow(
    tabIds: number[],
    workspaceId: string,
    workspaceName: string
  ): Promise<OperationResult<void>> {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }

      console.log(`移动 ${tabIds.length} 个标签页到全局专用窗口（来自工作区: ${workspaceName}）`);

      // 确保全局专用窗口存在
      const windowResult = await this.getOrCreateGlobalWorkspaceWindow();
      if (!windowResult.success) {
        return { success: false, error: windowResult.error };
      }

      const windowId = windowResult.data!.id;

      // 移动标签页到专用窗口
      await chrome.tabs.move(tabIds, {
        windowId: windowId,
        index: -1 // 移动到窗口末尾
      });

      console.log(`成功移动 ${tabIds.length} 个标签页到工作区专用窗口 ${windowId}`);

      return { success: true };
    } catch (error) {
      console.error(`移动标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs to workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 将特定工作区的标签页从全局专用窗口移动到主窗口
   */
  static async moveTabsFromWorkspaceWindow(
    workspaceId: string,
    targetWindowId?: number
  ): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`从全局专用窗口移动工作区 ${workspaceId} 的标签页到主窗口`);

      // 使用全局专用窗口ID
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        console.log(`全局专用窗口不存在`);
        return { success: true, data: [] };
      }

      // 获取目标工作区信息
      const { StorageManager } = await import('./storage');
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        console.log(`获取工作区 ${workspaceId} 信息失败:`, workspaceResult.error);
        return { success: true, data: [] };
      }

      const workspace = workspaceResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      // 获取专用窗口中的所有标签页
      const tabs = await chrome.tabs.query({ windowId });

      // 过滤出属于目标工作区的标签页
      const workspaceTabs = tabs.filter(tab => {
        if (tab.url?.includes('workspace-placeholder.html')) {
          return false; // 排除占位符页面
        }
        return workspaceUrls.some(url => tab.url?.startsWith(url));
      });

      console.log(`在全局专用窗口中找到工作区 "${workspace.name}" 的 ${workspaceTabs.length} 个标签页`);

      if (workspaceTabs.length === 0) {
        console.log(`全局专用窗口中没有工作区 "${workspace.name}" 的标签页需要移动`);
        return { success: true, data: [] };
      }

      // 确定目标窗口
      let targetWindow = targetWindowId;
      if (!targetWindow) {
        // 获取当前活跃窗口作为目标
        const currentWindow = await chrome.windows.getCurrent();
        targetWindow = currentWindow.id!;
      }

      console.log(`从全局专用窗口 ${windowId} 移动 ${workspaceTabs.length} 个标签页到窗口 ${targetWindow}`);

      // 移动标签页
      const tabIds = workspaceTabs.map(tab => tab.id!);
      await chrome.tabs.move(tabIds, {
        windowId: targetWindow,
        index: -1
      });

      // 转换为 TabInfo 格式
      const tabInfos: TabInfo[] = workspaceTabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: targetWindow!,
      }));

      console.log(`成功移动 ${workspaceTabs.length} 个标签页到主窗口`);

      return { success: true, data: tabInfos };
    } catch (error) {
      console.error(`从全局专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs from global workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭全局专用窗口
   */
  static async closeWorkspaceWindow(workspaceId: string): Promise<OperationResult<void>> {
    return await this.closeGlobalWorkspaceWindow();
  }

  /**
   * 关闭全局专用窗口
   */
  static async closeGlobalWorkspaceWindow(): Promise<OperationResult<void>> {
    try {
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        console.log(`全局专用窗口不存在，无需关闭`);
        return { success: true };
      }

      console.log(`关闭全局专用窗口: ${windowId}`);

      // 先移动所有标签页到主窗口
      await this.moveTabsFromWorkspaceWindow('global');

      // 关闭专用窗口
      await chrome.windows.remove(windowId);

      // 清理引用
      this.globalWorkspaceWindowId = null;

      console.log(`成功关闭全局专用窗口: ${windowId}`);

      return { success: true };
    } catch (error) {
      console.error(`关闭全局专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to close global workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 获取所有工作区专用窗口信息（现在只有一个全局窗口）
   */
  static async getAllWorkspaceWindows(): Promise<OperationResult<WindowInfo[]>> {
    try {
      const windowInfos: WindowInfo[] = [];

      if (this.globalWorkspaceWindowId) {
        try {
          const window = await chrome.windows.get(this.globalWorkspaceWindowId, { populate: true });

          windowInfos.push({
            id: this.globalWorkspaceWindowId,
            workspaceId: 'global',
            workspaceName: '全局工作区专用窗口',
            tabCount: window.tabs?.length || 0,
            isVisible: window.state !== 'minimized'
          });
        } catch {
          // 窗口不存在，清理引用
          this.globalWorkspaceWindowId = null;
        }
      }

      return { success: true, data: windowInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to get workspace windows',
          details: error,
        },
      };
    }
  }

  /**
   * 更新窗口标题（现在更新全局窗口标题）
   */
  static async updateWindowTitle(
    workspaceId: string,
    workspaceName: string,
    tabCount: number
  ): Promise<OperationResult<void>> {
    try {
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        return { success: true }; // 窗口不存在，忽略
      }

      // 通过更新占位符页面的标题来间接更新窗口标题
      const tabs = await chrome.tabs.query({ windowId });
      const placeholderTab = tabs.find(tab =>
        tab.url?.includes('workspace-placeholder.html')
      );

      if (placeholderTab) {
        const newUrl = chrome.runtime.getURL('workspace-placeholder.html') +
          `?workspaceId=global&workspaceName=${encodeURIComponent('全局工作区专用窗口')}&tabCount=${tabCount}`;

        await chrome.tabs.update(placeholderTab.id!, { url: newUrl });
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: 'Failed to update window title',
          details: error,
        },
      };
    }
  }
}
