import {
  WorkSpace,
  WorkspaceSwitchOptions,
  OperationResult
} from '@/types/workspace';
import { StorageManager } from './storage';
import { TabManager } from './tabs';
import { WorkspaceManager } from './workspace';
import { WindowManager } from './windowManager';
import { ERROR_CODES } from './constants';

/**
 * 工作区切换管理类
 */
export class WorkspaceSwitcher {
  /**
   * 切换到指定工作区（专用窗口架构）
   */
  static async switchToWorkspace(
    workspaceId: string,
    options: WorkspaceSwitchOptions = {}
  ): Promise<OperationResult<void>> {
    try {
      console.log(`开始切换到工作区: ${workspaceId}`);

      // 获取目标工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 获取当前活跃工作区
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;

      // 获取设置
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) {
        return { success: false, error: settingsResult.error };
      }

      const settings = settingsResult.data!;

      // 合并选项和设置
      const switchOptions: WorkspaceSwitchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? true,
      };

      // 1. 处理当前窗口中的标签页
      if (currentWorkspace && currentWorkspace.id !== workspaceId) {
        // 如果有当前工作区且不是目标工作区，将其标签页移动到专用窗口
        await this.moveCurrentTabsToWorkspaceWindow(currentWorkspace);
      } else if (!currentWorkspace) {
        // 如果没有当前工作区，检查是否有其他工作区的标签页需要移动
        await this.moveNonTargetWorkspaceTabsToWindow(workspaceId);
      }

      // 2. 从目标工作区的专用窗口移动标签页到主窗口
      await this.moveTabsFromWorkspaceWindow(workspace);

      // 3. 智能检查并打开工作区中缺失的网站
      await this.openWorkspaceWebsites(workspace);

      // 4. 设置为活跃工作区
      await StorageManager.setActiveWorkspaceId(workspaceId);

      // 5. 更新工作区状态
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data!;
        workspaces.forEach(w => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }

      // 6. 如果需要，聚焦到第一个标签页
      if (switchOptions.focusFirstTab && workspace.websites.length > 0) {
        const firstWebsite = workspace.websites[0];
        const tabResult = await TabManager.findTabByUrl(firstWebsite.url);
        if (tabResult.success && tabResult.data) {
          await TabManager.activateTab(tabResult.data.id);
        }
      }

      console.log(`成功切换到工作区: ${workspace.name}`);
      return { success: true };
    } catch (error) {
      console.error(`切换工作区失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 移动非目标工作区的标签页到专用窗口
   */
  private static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId: string): Promise<OperationResult<void>> {
    try {
      console.log(`检查并移动非目标工作区的标签页到专用窗口，目标工作区: ${targetWorkspaceId}`);

      // 获取所有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.log('获取工作区列表失败:', workspacesResult.error);
        return { success: true }; // 不阻断流程
      }

      const workspaces = workspacesResult.data!;

      // 获取当前窗口的所有标签页
      const currentTabsResult = await TabManager.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log('获取当前窗口标签页失败:', currentTabsResult.error);
        return { success: true };
      }

      const currentTabs = currentTabsResult.data!;
      console.log(`当前窗口共有 ${currentTabs.length} 个标签页`);

      // 检查每个非目标工作区，看是否有相关标签页需要移动
      for (const workspace of workspaces) {
        if (workspace.id === targetWorkspaceId) {
          continue; // 跳过目标工作区
        }

        const workspaceUrls = workspace.websites.map(w => w.url);
        const relatedTabs = currentTabs.filter(tab =>
          workspaceUrls.some(url => tab.url.startsWith(url))
        );

        if (relatedTabs.length > 0) {
          console.log(`发现工作区 "${workspace.name}" 的 ${relatedTabs.length} 个标签页需要移动到专用窗口`);

          const tabIds = relatedTabs.map(tab => tab.id);
          const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
            tabIds,
            workspace.id,
            workspace.name
          );

          if (moveResult.success) {
            console.log(`成功移动工作区 "${workspace.name}" 的 ${tabIds.length} 个标签页到专用窗口`);
          } else {
            console.error(`移动工作区 "${workspace.name}" 的标签页失败:`, moveResult.error);
          }
        }
      }

      return { success: true };
    } catch (error) {
      console.error('移动非目标工作区标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move non-target workspace tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 将当前窗口的工作区相关标签页移动到专用窗口
   */
  private static async moveCurrentTabsToWorkspaceWindow(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`将工作区 ${workspace.name} 的标签页移动到专用窗口`);

      // 获取当前窗口中与工作区相关的标签页
      const workspaceTabsResult = await TabManager.getWorkspaceRelatedTabs(workspace);
      if (!workspaceTabsResult.success) {
        console.log(`获取工作区相关标签页失败:`, workspaceTabsResult.error);
        return { success: true }; // 不阻断流程
      }

      const workspaceTabs = workspaceTabsResult.data!;
      if (workspaceTabs.length === 0) {
        console.log(`工作区 ${workspace.name} 没有相关标签页需要移动`);
        return { success: true };
      }

      // 移动标签页到专用窗口
      const tabIds = workspaceTabs.map(tab => tab.id);
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        workspace.id,
        workspace.name
      );

      if (moveResult.success) {
        console.log(`成功移动 ${tabIds.length} 个标签页到工作区专用窗口`);
      } else {
        console.error(`移动标签页到专用窗口失败:`, moveResult.error);
      }

      return moveResult;
    } catch (error) {
      console.error(`移动当前标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move current tabs to workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 从工作区专用窗口移动标签页到当前窗口（优化版）
   */
  private static async moveTabsFromWorkspaceWindow(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`📥 从工作区 ${workspace.name} 的专用窗口移动标签页到主窗口`);

      // 从专用窗口移动标签页
      const moveResult = await WindowManager.moveTabsFromWorkspaceWindow(workspace.id);

      if (moveResult.success) {
        const movedTabs = moveResult.data!;
        console.log(`✅ 成功从专用窗口移动 ${movedTabs.length} 个标签页到主窗口`);

        // 如果移动了标签页，添加短暂延迟确保标签页完全加载到主窗口
        if (movedTabs.length > 0) {
          console.log('⏳ 等待标签页完全加载到主窗口...');
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      } else {
        console.error(`❌ 从专用窗口移动标签页失败:`, moveResult.error);
      }

      return { success: true }; // 即使失败也不阻断流程
    } catch (error) {
      console.error(`❌ 从工作区专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs from workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 智能检查并自动打开工作区中缺失的网站（修复版）
   */
  private static async openWorkspaceWebsites(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`🔍 智能检查并打开工作区 ${workspace.name} 中缺失的网站`);

      // 如果工作区没有配置网站，处理空工作区情况
      if (!workspace.websites || workspace.websites.length === 0) {
        console.log(`⚠️ 工作区 "${workspace.name}" 没有配置任何网站`);
        const currentTabsResult = await TabManager.getCurrentWindowTabs();
        if (currentTabsResult.success) {
          const currentTabs = currentTabsResult.data!;
          if (currentTabs.length === 0) {
            console.log('📝 主窗口没有标签页，为空工作区创建默认新标签页');
            const newTabResult = await TabManager.createTab('chrome://newtab/', false, true);
            if (newTabResult.success) {
              console.log('✅ 为空工作区成功创建默认新标签页');
            } else {
              console.error('❌ 为空工作区创建默认新标签页失败:', newTabResult.error);
            }
          } else {
            console.log(`✅ 主窗口已有 ${currentTabs.length} 个标签页，空工作区无需额外操作`);
          }
        }
        return { success: true };
      }

      // 添加延迟确保标签页移动操作完全完成
      console.log('⏳ 等待标签页移动操作完全完成...');
      await new Promise(resolve => setTimeout(resolve, 100));

      // 获取当前窗口的所有标签页
      const currentTabsResult = await TabManager.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log('❌ 获取当前窗口标签页失败:', currentTabsResult.error);
        return { success: true }; // 不阻断流程
      }

      const currentTabs = currentTabsResult.data!;
      const currentUrls = currentTabs.map(tab => tab.url);
      console.log(`📋 当前窗口已有 ${currentTabs.length} 个标签页:`, currentUrls);

      // 检查每个工作区网站是否已存在
      const missingWebsites = [];
      for (const website of workspace.websites) {
        const isAlreadyOpen = currentUrls.some(url => url.startsWith(website.url));
        if (!isAlreadyOpen) {
          missingWebsites.push(website);
          console.log(`🔍 发现缺失的网站: ${website.title} (${website.url})`);
        } else {
          console.log(`✅ 网站已存在: ${website.title} (${website.url})`);
        }
      }

      if (missingWebsites.length === 0) {
        console.log(`✅ 工作区 "${workspace.name}" 的所有网站都已打开，无需创建新标签页`);
        // 确保主窗口至少有一个标签页
        if (currentTabs.length === 0) {
          console.log('⚠️ 主窗口没有标签页，创建默认新标签页');
          await this.ensureMainWindowHasTab();
        }
        return { success: true };
      }

      console.log(`🚀 需要打开 ${missingWebsites.length} 个缺失的网站`);
      let successCount = 0;
      let failCount = 0;

      for (const website of missingWebsites) {
        try {
          console.log(`📝 正在创建标签页: ${website.title} (${website.url})`);
          const newTabResult = await TabManager.createTab(
            website.url,
            website.isPinned || false, // 使用网站配置的固定状态
            false // 不立即激活，保持当前标签页活跃
          );
          if (newTabResult.success) {
            console.log(`✅ 成功创建标签页: ${website.title}`);
            successCount++;
          } else {
            console.error(`❌ 创建标签页失败 ${website.title}:`, newTabResult.error);
            failCount++;
          }
        } catch (error) {
          console.error(`❌ 处理网站 ${website.title} 时出错:`, error);
          failCount++;
        }
      }

      console.log(`🎯 工作区 "${workspace.name}" 缺失网站打开完成: 成功 ${successCount} 个，失败 ${failCount} 个`);
      return { success: true };
    } catch (error) {
      console.error('❌ 自动打开缺失网站时出错:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to open workspace websites',
          details: error,
        },
      };
    }
  }

  /**
   * 确保主窗口至少有一个标签页
   */
  private static async ensureMainWindowHasTab(): Promise<OperationResult<void>> {
    try {
      const newTabResult = await TabManager.createTab('chrome://newtab/', false, true);
      if (newTabResult.success) {
        console.log('✅ 成功创建默认新标签页');
      } else {
        console.error('❌ 创建默认新标签页失败:', newTabResult.error);
      }
      return { success: true };
    } catch (error) {
      console.error('❌ 确保主窗口有标签页时出错:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to ensure main window has tab',
          details: error,
        },
      };
    }
  }



  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) {
        return { success: false, error: activeIdResult.error };
      }

      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }

      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        // 如果工作区不存在，清除活跃状态
        await StorageManager.setActiveWorkspaceId(null);
        return { success: true, data: null };
      }

      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get current workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 智能检测当前应该激活的工作区
   */
  static async detectActiveWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }

      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }

      const activeTab = activeTabResult.data!;

      // 查找包含当前活跃标签页URL的工作区
      const matchingWorkspace = workspaces.find(workspace =>
        workspace.websites.some(website => 
          activeTab.url.startsWith(website.url)
        )
      );

      return { success: true, data: matchingWorkspace || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to detect active workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 添加当前标签页到工作区
   */
  static async addCurrentTabToWorkspace(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: false, error: activeTabResult.error };
      }

      const activeTab = activeTabResult.data!;

      // 添加到工作区
      const addResult = await WorkspaceManager.addWebsite(
        workspaceId, 
        activeTab.url, 
        {
          title: activeTab.title,
          favicon: activeTab.favicon,
          pinTab: activeTab.isPinned,
        }
      );

      if (addResult.success) {
        return { success: true };
      } else {
        return { success: false, error: addResult.error };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to add current tab to workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace(): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'No workspaces available',
          },
        };
      }

      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) {
        return { success: false, error: currentResult.error };
      }

      const currentWorkspace = currentResult.data;
      let nextIndex = 0;

      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex(w => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }

      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch to next workspace',
          details: error,
        },
      };
    }
  }
}
