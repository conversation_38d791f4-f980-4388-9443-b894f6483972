import React, { useState } from 'react';
import { WorkSpace } from '@/types/workspace';
import WorkspaceItem from './WorkspaceItem';

interface WorkspaceListProps {
  workspaces: WorkSpace[];
  activeWorkspaceId: string | null;
  onSwitchWorkspace: (workspaceId: string) => void;
  onUpdateWorkspace: (id: string, updates: { name?: string; icon?: string; color?: string }) => void;
  onDeleteWorkspace: (id: string) => void;
  onAddCurrentTab: (workspaceId: string) => void;
  onAddWebsiteUrl: (workspaceId: string, url: string) => void;
  onRemoveWebsite: (workspaceId: string, websiteId: string) => void;
  onUpdateWebsite: (workspaceId: string, websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => void;
  onReorderWorkspaces: (workspaceIds: string[]) => void;
  onReorderWebsites: (workspaceId: string, websiteIds: string[]) => void;
  onToggleUserTabsVisibility?: (workspaceId: string) => void; // 新增：切换用户标签页显示状态
}

/**
 * 工作区列表组件
 */
const WorkspaceList: React.FC<WorkspaceListProps> = ({
  workspaces,
  activeWorkspaceId,
  onSwitchWorkspace,
  onUpdateWorkspace,
  onDeleteWorkspace,
  onAddCurrentTab,
  onAddWebsiteUrl,
  onRemoveWebsite,
  onUpdateWebsite,
  onReorderWorkspaces: _onReorderWorkspaces,
  onReorderWebsites,
  onToggleUserTabsVisibility,
}) => {
  const [expandedWorkspaceId, setExpandedWorkspaceId] = useState<string | null>(
    activeWorkspaceId
  );

  /**
   * 处理工作区展开/折叠
   */
  const handleToggleExpand = (workspaceId: string) => {
    setExpandedWorkspaceId(
      expandedWorkspaceId === workspaceId ? null : workspaceId
    );
  };

  /**
   * 处理工作区点击
   */
  const handleWorkspaceClick = (workspaceId: string) => {
    // 如果点击的是当前活跃工作区，则切换展开状态
    if (workspaceId === activeWorkspaceId) {
      handleToggleExpand(workspaceId);
    } else {
      // 否则切换工作区并展开
      onSwitchWorkspace(workspaceId);
      setExpandedWorkspaceId(workspaceId);
    }
  };

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-3">
      {workspaces.map((workspace) => (
        <WorkspaceItem
          key={workspace.id}
          workspace={workspace}
          isActive={workspace.id === activeWorkspaceId}
          isExpanded={workspace.id === expandedWorkspaceId}
          onWorkspaceClick={() => handleWorkspaceClick(workspace.id)}
          onToggleExpand={() => handleToggleExpand(workspace.id)}
          onUpdateWorkspace={(updates) => onUpdateWorkspace(workspace.id, updates)}
          onDeleteWorkspace={() => onDeleteWorkspace(workspace.id)}
          onAddCurrentTab={() => onAddCurrentTab(workspace.id)}
          onAddWebsiteUrl={(url) => onAddWebsiteUrl(workspace.id, url)}
          onRemoveWebsite={(websiteId) => onRemoveWebsite(workspace.id, websiteId)}
          onUpdateWebsite={(websiteId, updates) => onUpdateWebsite(workspace.id, websiteId, updates)}
          onReorderWebsites={(websiteIds) => onReorderWebsites(workspace.id, websiteIds)}
          onToggleUserTabsVisibility={onToggleUserTabsVisibility ? () => onToggleUserTabsVisibility(workspace.id) : undefined}
        />
      ))}
    </div>
  );
};

export default WorkspaceList;
